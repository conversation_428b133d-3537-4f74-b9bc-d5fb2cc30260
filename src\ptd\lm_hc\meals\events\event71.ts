import { <PERSON><PERSON><PERSON>, Player } from '@minecraft/server';
import { excludedEntitiesPlayerForAll } from '../../utilities/entityQueries';
import { onEatMeal } from '../events';

/**
 * Constants for the "Begone, Monsters!" event
 */
const SEARCH_RADIUS: number = 25; // Radius to search for hostile mobs
const EFFECT_DURATION: number = 30 * 20; // 30 seconds in ticks

/**
 * Event 71: Begone, Monsters! - Turns all hostile mobs invisible within a 25 block radius
 * Makes hostile mobs harder to see and fight
 *
 * @param player - The player who triggered the event
 */
export function event71(player: Player): void {
  try {
    const dimension = player.dimension;
    const playerPos = player.location;

    // Get all entities around the player (excluding technical entities using the pre-defined query)
    const nearbyEntities = dimension.getEntities({
      location: playerPos,
      maxDistance: SEARCH_RADIUS,
      ...excludedEntitiesPlayerForAll
    });

    if (nearbyEntities.length === 0) {
      onEatMeal(player);
      return;
    }

    // Filter to keep only hostile mobs
    const hostileMobs = nearbyEntities.filter((entity) => isHostileMob(entity));
    let invisibleCount = 0;

    // Apply invisibility to all hostile mobs
    for (const mob of hostileMobs) {
      if (mob.isValid()) {
        try {
          // Apply invisibility effect
          mob.addEffect('invisibility', EFFECT_DURATION, {
            amplifier: 0,
            showParticles: false
          });

          // Add ghost particles at mob location
          dimension.spawnParticle('minecraft:large_explosion', mob.location);
          dimension.playSound('mob.endermen.portal', mob.location);

          invisibleCount++;
        } catch (error) {
          console.warn(`Failed to make mob invisible: ${error}`);
        }
      }
    }
  } catch (error) {
    console.warn(`Failed to execute event 71: ${error}`);
  }
  return;
}

/**
 * Determines if an entity is a hostile mob
 *
 * @param entity - The entity to check
 * @returns True if the entity is considered a hostile mob
 */
function isHostileMob(entity: Entity): boolean {
  if (!entity || !entity.isValid()) return false;

  const hostileMobTypes = [
    'minecraft:zombie',
    'minecraft:skeleton',
    'minecraft:creeper',
    'minecraft:spider',
    'minecraft:cave_spider',
    'minecraft:enderman',
    'minecraft:witch',
    'minecraft:slime',
    'minecraft:magma_cube',
    'minecraft:blaze',
    'minecraft:ghast',
    'minecraft:phantom',
    'minecraft:drowned',
    'minecraft:guardian',
    'minecraft:elder_guardian',
    'minecraft:pillager',
    'minecraft:vindicator',
    'minecraft:evoker',
    'minecraft:ravager',
    'minecraft:vex',
    'minecraft:stray',
    'minecraft:husk',
    'minecraft:wither_skeleton',
    'minecraft:piglin',
    'minecraft:piglin_brute',
    'minecraft:zombified_piglin',
    'minecraft:hoglin',
    'minecraft:zoglin',
    'minecraft:warden',
    'minecraft:breeze',
    'minecraft:shulker',
    'minecraft:silverfish',
    'minecraft:endermite',
    'minecraft:illusioner'
  ];

  return hostileMobTypes.includes(entity.typeId);
}
