import { updateGuidebookAchievements } from './guidebook';
/**
 * @constant MAX_ACHIEVEMENTS
 * @description The total number of possible achievements
 */
export const MAX_ACHIEVEMENTS = 75;
/**
 * @constant ACHIEVEMENTS_PER_PAGE
 * @description The number of achievements shown per guidebook page
 */
export const ACHIEVEMENTS_PER_PAGE = 5;
/**
 * @constant FIRST_ACHIEVEMENT_PAGE
 * @description The first page number in the guidebook that shows achievements
 */
export const FIRST_ACHIEVEMENT_PAGE = 7;
/**
 * @constant MAX_ACHIEVEMENT_PAGE
 * @description The last page number in the guidebook that shows achievements
 */
export const MAX_ACHIEVEMENT_PAGE = 21;
/**
 * @constant PLAYER_CHUNK_SIZE
 * @description The number of achievements stored in each player dynamic property chunk
 */
export const PLAYER_CHUNK_SIZE = 32;
/**
 * Validates that a number is a valid achievement number (1-75)
 * @param num Number to validate
 * @returns The number cast as an AchievementNumber if valid
 * @throws Error if number is invalid
 */
function validateAchievementNumber(num) {
    if (!Number.isInteger(num) || num < 1 || num > MAX_ACHIEVEMENTS) {
        throw new Error(`Invalid achievement number: ${num}. Must be between 1 and ${MAX_ACHIEVEMENTS}`);
    }
    return num;
}
/**
 * Maps an achievement number to its page and local index
 * @param achievementNum - The achievement number (1-75)
 * @returns Object containing page number and local index (0-4) within that page
 */
export function getAchievementPageInfo(achievementNum) {
    const validNum = validateAchievementNumber(achievementNum);
    // Calculate page number (pages 7-21 contain achievements)
    const page = Math.floor((validNum - 1) / ACHIEVEMENTS_PER_PAGE) + FIRST_ACHIEVEMENT_PAGE;
    // Calculate local index within the page (0-4)
    const localIndex = (validNum - 1) % ACHIEVEMENTS_PER_PAGE;
    return { page, localIndex };
}
/**
 * Gets the player dynamic property name for storing a chunk of achievements
 * @param chunkIndex - Index of the chunk (0-based)
 * @returns The dynamic property name for that chunk
 */
function getPlayerAchievementChunkProperty(chunkIndex) {
    return `ptd_lmhc:achievements_chunk${chunkIndex + 1}`;
}
/**
 * Gets the entity property name for storing achievements for a specific page
 * @param page - The guidebook page number (7-21)
 * @returns The entity property name for that page's achievements
 */
function getPagePropertyName(page) {
    if (page < FIRST_ACHIEVEMENT_PAGE || page > MAX_ACHIEVEMENT_PAGE) {
        throw new Error(`Invalid achievement page: ${page}. Must be between ${FIRST_ACHIEVEMENT_PAGE} and ${MAX_ACHIEVEMENT_PAGE}`);
    }
    return `ptd_lmhc:achievements_page${page}`;
}
/**
 * Gets the chunk index and bit position for a specific achievement
 * @param achievementNum - The achievement number (1-75)
 * @returns Object with chunk index and bit position
 */
function getAchievementChunkInfo(achievementNum) {
    const validNum = validateAchievementNumber(achievementNum);
    const chunkIndex = Math.floor((validNum - 1) / PLAYER_CHUNK_SIZE);
    const bitPosition = validNum % PLAYER_CHUNK_SIZE;
    return { chunkIndex, bitPosition };
}
/**
 * Updates a player's achievement status and syncs it with nearby guidebooks
 *
 * @param player - The player who earned the achievement
 * @param achievementNum - The number of the achievement (1-75)
 * @param achieved - Boolean indicating if the achievement was earned or revoked
 * @remarks
 * - Uses chunked properties to store achievement states
 * - Each bit position represents one achievement
 * - Automatically updates any nearby guidebooks belonging to this player
 * - Only updates the specific page property that contains this achievement
 *
 * @example
 * ```typescript
 * // When player completes achievement 1
 * updateAchievements(player, 1, true);
 * ```
 */
export function updateAchievements(player, achievementNum, achieved) {
    try {
        const validNum = validateAchievementNumber(achievementNum);
        const { chunkIndex, bitPosition } = getAchievementChunkInfo(validNum);
        // Get the property name for this chunk
        const chunkPropertyName = getPlayerAchievementChunkProperty(chunkIndex);
        // Get current chunk value or initialize to 0
        let chunkValue = player.getDynamicProperty(chunkPropertyName) || 0;
        if (achieved) {
            // Check if achievement was not already unlocked
            const wasUnlocked = (chunkValue & (1 << bitPosition)) !== 0;
            // Set the bit for this achievement
            chunkValue |= 1 << bitPosition;
            // Only play sound and send message if this is a new unlock
            if (!wasUnlocked) {
                player.dimension.playSound('random.levelup', player.location);
                // Send a message to the player that unlocked the achievement
                player.sendMessage(`Event §2${achievementNum}§r triggered`);
            }
        }
        else {
            // Clear the bit for this achievement
            chunkValue &= ~(1 << bitPosition);
        }
        // Update player's achievement chunk
        player.setDynamicProperty(chunkPropertyName, chunkValue);
        // Update any nearby guidebooks with new achievement state
        updateGuidebookAchievements(player);
    }
    catch (error) {
        console.warn(`Failed to update achievement ${achievementNum}: ${error}`);
    }
}
/**
 * Checks if a player has completed a specific achievement
 *
 * @param player - The player to check
 * @param achievementNum - The achievement number to check (1-75)
 * @returns boolean indicating if the achievement is completed
 */
export function hasAchievement(player, achievementNum) {
    try {
        const validNum = validateAchievementNumber(achievementNum);
        const { chunkIndex, bitPosition } = getAchievementChunkInfo(validNum);
        // Get the property name for this chunk
        const chunkPropertyName = getPlayerAchievementChunkProperty(chunkIndex);
        // Get current chunk value or initialize to 0
        const chunkValue = player.getDynamicProperty(chunkPropertyName) || 0;
        // Check if the bit is set
        return (chunkValue & (1 << bitPosition)) !== 0;
    }
    catch (error) {
        console.warn(`Failed to check achievement ${achievementNum}: ${error}`);
        return false;
    }
}
/**
 * Updates the achievement properties for a guidebook entity based on the player's achievements
 * @param guidebook - The guidebook entity to update
 * @param player - The player whose achievements to use
 */
export function updateGuidebookPageAchievements(guidebook, player, page) {
    try {
        if (page < FIRST_ACHIEVEMENT_PAGE || page > MAX_ACHIEVEMENT_PAGE) {
            return; // Not an achievement page
        }
        // Calculate which achievements are on this page
        const firstAchievementOnPage = (page - FIRST_ACHIEVEMENT_PAGE) * ACHIEVEMENTS_PER_PAGE + 1;
        // Calculate bitmask for this page (only 5 bits needed)
        let pageBitmask = 0;
        for (let i = 0; i < ACHIEVEMENTS_PER_PAGE; i++) {
            const achievementNum = firstAchievementOnPage + i;
            if (achievementNum <= MAX_ACHIEVEMENTS) {
                if (hasAchievement(player, achievementNum)) {
                    pageBitmask |= 1 << i;
                }
            }
        }
        // Update the guidebook's page-specific property
        const pagePropertyName = getPagePropertyName(page);
        guidebook.setProperty(pagePropertyName, pageBitmask);
    }
    catch (error) {
        console.warn(`Failed to update guidebook page achievements for page ${page}: ${error}`);
    }
}
/**
 * Updates all achievement page properties for a guidebook entity
 * @param guidebook - The guidebook entity to update
 * @param player - The player whose achievements to use
 */
export function updateAllGuidebookAchievements(guidebook, player) {
    for (let page = FIRST_ACHIEVEMENT_PAGE; page <= MAX_ACHIEVEMENT_PAGE; page++) {
        updateGuidebookPageAchievements(guidebook, player, page);
    }
}
/**
 * Initializes a player's achievement data in the new system
 *
 * Since the project isn't released yet, we don't need to migrate existing data.
 * This function simply ensures all achievement chunks are properly initialized.
 *
 * @param player - The player to initialize achievements for
 * @returns Always returns true
 */
export function initializePlayerAchievements(player) {
    try {
        if (!player || !player.isValid()) {
            console.warn('Cannot initialize achievements for invalid player');
            return false;
        }
        // Initialize all chunks to 0
        const requiredChunks = Math.ceil(MAX_ACHIEVEMENTS / PLAYER_CHUNK_SIZE);
        // Store each chunk in its own property
        for (let i = 0; i < requiredChunks; i++) {
            const propertyName = getPlayerAchievementChunkProperty(i);
            // Only set if not already set
            if (player.getDynamicProperty(propertyName) === undefined) {
                player.setDynamicProperty(propertyName, 0);
            }
        }
        return true;
    }
    catch (error) {
        console.warn(`Failed to initialize player achievements: ${error}`);
        return false;
    }
}
