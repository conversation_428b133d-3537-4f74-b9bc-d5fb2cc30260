import { Player, Entity } from '@minecraft/server';
import { onEatMeal } from '../events';
import { getRandomLocation } from '../../utilities/vector3';
import { EntityQuantityConfig, spawnEntitiesWithInterval } from '../../utilities/summonEntity';

// Constants for enderman spawning
const SPAWN_DELAY_TICKS: number = 5; // 0.25 seconds between spawns

/**
 * Event 40. Hostile Enderman
 *  Spawns 10 angry endermen that attack the player.
 * @param player The player who triggers the event
 */
export function event40(player: Player): void {
  try {
    // Configure the entity type and quantity to spawn
    const entityConfigs: EntityQuantityConfig[] = [{ entityId: 'ptd_lmhc:hostile_enderman', count: 10 }];

    // Create a location callback function that returns a random position around the player
    const getSpawnLocation = () => {
      const spawnPos = getRandomLocation(
        player.location,
        player.dimension,
        9, // minDistance
        4, // additionalOffset (maxDistance = 13)
        0,
        true
      );

      // Play particle effect at spawn location if position is valid
      if (spawnPos) {
        player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnPos);
        player.dimension.playSound('mob.endermen.portal', spawnPos);
      }

      return spawnPos;
    };

    // Define a callback to be executed when an enderman is spawned to make it angry
    const onEndermanSpawned = (enderman: Entity) => {
      try {
        // Make the enderman angry at the player immediately after spawning
        if (enderman && enderman.isValid()) {
          enderman.triggerEvent('minecraft:become_angry');

          // The enderman will naturally target the nearest player
        }
      } catch (error) {
        console.warn(`Failed to make enderman angry: ${error}`);
      }
    };

    // Start spawning angry endermen with the specified delay between each
    spawnEntitiesWithInterval(
      player.dimension,
      entityConfigs,
      getSpawnLocation,
      SPAWN_DELAY_TICKS,
      onEndermanSpawned
    ).catch((error) => {
      console.warn(`Error in event40 spawning process: ${error}`);
      onEatMeal(player); // Retry on error
    });
  } catch (error) {
    console.warn(`Failed to execute event 40: ${error}`);
    onEatMeal(player); // Retry on error
  }
  return;
}
