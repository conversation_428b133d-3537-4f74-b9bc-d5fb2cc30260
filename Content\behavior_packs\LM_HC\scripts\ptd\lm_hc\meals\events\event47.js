import { system } from '@minecraft/server';
import { getRandomInt } from '../../utilities/rng';
import { onEatMeal } from '../events';
// Define passive mob types to convert hostile mobs into
const passiveMobs = ['minecraft:cow', 'minecraft:chicken', 'minecraft:sheep', 'minecraft:pig'];
/**
 * Event 47: Mob Convert
 * Converts hostile mobs to passive mobs (cow, chicken, sheep, pig)
 * @param player The player who triggered the event
 */
export function event47(player) {
    try {
        // Find hostile mobs around the player
        const queryOptions = {
            families: ['monster'],
            location: player.location,
            maxDistance: 64
        };
        const hostileMobs = player.dimension.getEntities(queryOptions);
        // If no hostile mobs found, call another random event
        if (hostileMobs.length === 0) {
            onEatMeal(player);
            return;
        }
        let convertCount = 0;
        const maxConvertCount = hostileMobs.length;
        const convertInterval = system.runInterval(() => {
            try {
                if (convertCount >= maxConvertCount) {
                    system.clearRun(convertInterval);
                    return;
                }
                const hostileMob = hostileMobs[convertCount];
                // Skip if entity no longer exists
                if (!hostileMob || !hostileMob.isValid()) {
                    convertCount++;
                    return;
                }
                // Select a random passive mob type
                const passiveMobType = passiveMobs[getRandomInt(0, passiveMobs.length - 1)];
                // Skip if we somehow got an undefined mob type
                if (!passiveMobType) {
                    convertCount++;
                    return;
                }
                // Get the hostile mob's location
                const mobLocation = hostileMob.location;
                // Spawn particles at the location
                player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', mobLocation);
                player.dimension.playSound('mob.endermen.portal', mobLocation);
                // Remove the hostile mob
                hostileMob.remove();
                // Spawn the passive mob
                player.dimension.spawnEntity(passiveMobType, mobLocation);
                convertCount++;
            }
            catch (error) {
                // Silently handle errors for individual mob conversions
                // Increment counter to continue with next mob
                convertCount++;
                // Optional: Log the error for debugging purposes
                console.debug(`Error converting mob at index ${convertCount - 1}: ${error}`);
            }
        }, 1);
    }
    catch (error) {
        console.warn(`Failed to execute mob convert event: ${error}`);
    }
    return;
}
