import {
    world,
    ItemStack,
    Player,
    EntityEquippableComponent,
    EntityComponentTypes,
    EquipmentSlot
} from "@minecraft/server";

/**
 * Registers the event handler to give the guidebook item to a player in their main hand on first spawn.
 * Uses a persistent dynamic property to ensure it is only given once per player.
 * Uses the entity equipment component for main hand placement.
 */
export function registerFirstSpawnGuidebook(): void {
    world.afterEvents.playerSpawn.subscribe((ev) => {
        const player: Player = ev.player;
        if (!player || !player.isValid()) return;

        // Use a persistent dynamic property to track if the player has received the guidebook
        const HAS_GUIDEBOOK_PROP = "ptd_lmhc:has_guidebook";
        if (player.getDynamicProperty(HAS_GUIDEBOOK_PROP)) return;

        // Get the player's equippable component
        const equippable = player.getComponent(EntityComponentTypes.Equippable) as EntityEquippableComponent;
        if (!equippable) {
            console.warn(`[LM_HC] Could not access equippable component for player ${player.name} (${player.id}) to give guidebook.`);
            return;
        }

        // Check if the main hand is empty
        const mainHandItem = equippable.getEquipment(EquipmentSlot.Mainhand);
        if (mainHandItem !== undefined) {
            // Main hand is not empty, do not overwrite
            console.warn(`[LM_HC] Player ${player.name} (${player.id}) did not receive guidebook: main hand not empty.`);
            return;
        }

        // Create the guidebook item stack
        const guidebookItem: ItemStack = new ItemStack("ptd_lmhc:guidebook", 1);

        // Set the guidebook in the main hand
        equippable.setEquipment(EquipmentSlot.Mainhand, guidebookItem);
        player.setDynamicProperty(HAS_GUIDEBOOK_PROP, true);
    });
}
