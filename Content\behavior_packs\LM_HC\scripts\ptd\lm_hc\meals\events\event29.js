import { GameMode, system } from '@minecraft/server';
/**
 * Event 29: Creative Mode
 * Temporarily switches the player's game mode to creative for 10 seconds
 * @param player The player who triggered the event
 */
export function event29(player) {
    try {
        const CONFIG = {
            DURATION: 200 // 10 seconds (20 ticks/second)
        };
        // Switch to creative mode
        player.setGameMode(GameMode.creative);
        player.onScreenDisplay.setActionBar('§6Creative Mode activated for 10 seconds!');
        // Set timeout to revert game mode
        system.runTimeout(() => {
            try {
                if (player.isValid()) {
                    player.setGameMode(GameMode.survival);
                    player.onScreenDisplay.setActionBar('§cCreative Mode deactivated!');
                }
            }
            catch (error) {
                console.warn(`Failed to revert game mode: ${error}`);
            }
        }, CONFIG.DURATION);
    }
    catch (error) {
        console.warn(`Failed to execute event 29: ${error}`);
    }
}
