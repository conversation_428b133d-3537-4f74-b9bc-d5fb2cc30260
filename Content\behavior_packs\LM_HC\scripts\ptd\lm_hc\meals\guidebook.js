import { system, world } from '@minecraft/server';
import { updateAllGuidebookAchievements, updateGuidebookPageAchievements, initializePlayerAchievements, FIRST_ACHIEVEMENT_PAGE, MAX_ACHIEVEMENT_PAGE } from './achievement';
// Constants
export const FOLLOW_DISTANCE = 2;
export const CHECK_INTERVAL = 1;
export const DYNAMIC_PROP_NAME = 'ptd_lmhc:guidebook_page';
/**
 * Guidebook component implementation
 */
export const guidebookComponent = {
    onUse(ev) {
        const source = ev.source;
        const itemStack = ev.itemStack;
        if (!itemStack)
            return;
        // Check if guidebook item was used
        if (itemStack.typeId === 'ptd_lmhc:guidebook') {
            const spawnLocation = source.getHeadLocation();
            spawnGuidebook(source, spawnLocation);
        }
    },
    onUseOn(ev) {
        const player = ev.source;
        const item = ev.itemStack;
        if (!item)
            return;
        if (item.typeId === 'ptd_lmhc:guidebook') {
            // Wait 1 tick for the guidebook to spawn
            system.waitTicks(1).then(() => {
                // Get the spawned guidebook entity
                const guidebooks = [
                    ...player.dimension.getEntities({
                        type: 'ptd_lmhc:guidebook',
                        location: ev.block.location,
                        maxDistance: 8
                    })
                ];
                // Get the most recently spawned guidebook
                const guidebook = guidebooks[guidebooks.length - 1];
                if (!guidebook)
                    return;
                // Tag guidebook with player ID
                guidebook.addTag(`${player.id}`);
            });
        }
    }
};
/**
 * Updates the player's dynamic property for the guidebook page.
 * @param player The player to update.
 * @param page The page number to set.
 */
export async function updatePlayerGuidebookPageProperty(player, guidebook) {
    await system.waitTicks(1);
    const currentPage = guidebook.getProperty('ptd_lmhc:pages');
    player.setDynamicProperty(DYNAMIC_PROP_NAME, currentPage);
}
/**
 * Process all guidebooks across all dimensions
 * This function handles updating guidebook positions to follow players
 * @param dimensions Array of dimensions to process guidebooks in
 */
export function processGuidebook(guidebook, dimension) {
    if (!guidebook.isValid())
        return;
    // Get player ID from guidebook tags
    const playerIdTag = guidebook.getTags().find((tag) => !tag.includes(':'));
    if (!playerIdTag) {
        guidebook.remove();
        return;
    }
    // Find the owner player
    const players = dimension.getPlayers({
        closest: 5,
        location: guidebook.location,
        maxDistance: 12
    });
    const player = players.find((p) => p.id === playerIdTag);
    if (!player) {
        // Player left the game or doesn't exist
        if (guidebook.isValid()) {
            // Save the current page state before removing
            const currentPage = guidebook.getProperty('ptd_lmhc:pages');
            const players = dimension.getPlayers({
                closest: 1,
                location: guidebook.location,
                maxDistance: 12,
                tags: [`${playerIdTag}`]
            });
            const owner = players[0];
            if (owner) {
                owner.setDynamicProperty(DYNAMIC_PROP_NAME, currentPage);
            }
            guidebook.remove();
        }
    }
    else {
        // Update guidebook position to follow player
        const location = player.getHeadLocation();
        const direction = player.getViewDirection();
        // Calculate adjusted position with proper offsets
        const targetPos = {
            x: location.x + direction.x * (FOLLOW_DISTANCE * 0.7),
            y: location.y - 0.5 + direction.y * (FOLLOW_DISTANCE * 0.7),
            z: location.z + direction.z * (FOLLOW_DISTANCE * 0.7)
        };
        try {
            guidebook.teleport(targetPos);
        }
        catch (error) {
            console.warn(`Failed to update guidebook position: ${error}`);
        }
    }
}
/**
 * Initialize the guidebook system
 * Note: The actual processing now happens in the global interval in main.ts
 */
export function initializeGuidebookSystem() {
    // No interval setup here - guidebook processing is now handled by the global interval
}
/**
 * Updates achievement properties for nearby guidebooks
 * @param player The player whose achievements changed
 */
export function updateGuidebookAchievements(player) {
    try {
        // Find nearby guidebooks belonging to this player
        const nearbyGuidebooks = player.dimension.getEntities({
            type: 'ptd_lmhc:guidebook',
            location: player.location,
            maxDistance: 12,
            tags: [`${player.id}`]
        });
        // Update achievement properties for each guidebook
        for (const guidebook of nearbyGuidebooks) {
            if (!guidebook || !guidebook.isValid())
                continue;
            // Initialize player's achievements if needed
            initializePlayerAchievements(player);
            // Get the current page of the guidebook
            const currentPage = guidebook.getProperty('ptd_lmhc:pages');
            // Only update properties for the current achievement page, for performance
            if (currentPage >= FIRST_ACHIEVEMENT_PAGE && currentPage <= MAX_ACHIEVEMENT_PAGE) {
                updateGuidebookPageAchievements(guidebook, player, currentPage);
            }
        }
    }
    catch (error) {
        console.warn(`Failed to update guidebook achievements: ${error}`);
    }
}
export async function spawnGuidebook(source, spawnLocation) {
    try {
        // Initialize player's achievements using the new system
        initializePlayerAchievements(source);
        const guidebookCount = source.dimension.getEntities({
            type: 'ptd_lmhc:guidebook',
            location: spawnLocation,
            closest: 1,
            tags: [`${source.id}`]
        });
        if (guidebookCount.length === 0) {
            const direction = source.getViewDirection();
            // Spawn guidebook in front of player
            // Calculate initial spawn position with adjusted offsets
            const spawnPos = {
                x: spawnLocation.x + direction.x * (FOLLOW_DISTANCE * 0.8),
                y: spawnLocation.y + 1.6,
                z: spawnLocation.z + direction.z * (FOLLOW_DISTANCE * 0.8)
            };
            // Adjust position to be more stable
            spawnPos.x -= direction.x * 0.2;
            spawnPos.z -= direction.z * 0.2;
            const guidebook = source.dimension.spawnEntity('ptd_lmhc:guidebook', spawnPos);
            // Tag guidebook with player ID
            guidebook.addTag(`${source.id}`);
            // Initialize property if needed and set initial page state
            if (!source.getDynamicProperty(DYNAMIC_PROP_NAME)) {
                updatePlayerGuidebookPageProperty(source, guidebook);
            }
            const currentPage = source.getDynamicProperty(DYNAMIC_PROP_NAME);
            guidebook.setProperty('ptd_lmhc:pages', currentPage);
            // Initialize all achievement page properties
            updateAllGuidebookAchievements(guidebook, source);
            await system.waitTicks(1);
            world.sendMessage(`Guidebook Page: ${guidebook.getProperty('ptd_lmhc:pages')}`);
        }
    }
    catch (error) {
        console.warn(`Failed to spawn guidebook: ${error}`);
    }
}
/**
 * Handles page changes to update achievement data when needed
 *
 * @param guidebook Guidebook entity that changed pages
 * @param player Owner of the guidebook
 * @param oldPage Previous page number
 * @param newPage New page number
 */
export function handleGuidebookPageChange(guidebook, player, newPage) {
    try {
        // Check if we're moving to an achievement page
        if (newPage >= FIRST_ACHIEVEMENT_PAGE && newPage <= MAX_ACHIEVEMENT_PAGE) {
            // Update the achievements for this specific page
            updateGuidebookPageAchievements(guidebook, player, newPage);
        }
    }
    catch (error) {
        console.warn(`Failed to handle guidebook page change: ${error}`);
    }
}
