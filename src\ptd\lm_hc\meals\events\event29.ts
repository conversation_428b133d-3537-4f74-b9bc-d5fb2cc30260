import { Player, system, Vector3, world } from "@minecraft/server";

// State management for flight mode
export const flightModeActive = new Map<string, boolean>();
export const slowDownValue = new Map<string, number>();
export const flightDuration = new Map<string, number>();

/**
 * Event 29: Flying Mode
 * Gives the player flight abilities for 60 seconds using adapted wings mechanics
 * @param player The player who triggered the event
 */
export function event29(player: Player): void {
  try {
    const CONFIG = {
      DURATION: 1200, // 60 seconds (20 ticks/second)
    } as const;

    const playerId: string = player.id;

    // Activate flight mode
    flightModeActive.set(playerId, true);
    slowDownValue.set(playerId, 0.05);
    flightDuration.set(playerId, CONFIG.DURATION);

    player.onScreenDisplay.setActionBar(
      "§bFlying Mode activated for 60 seconds!"
    );

    // Set timeout to deactivate flight mode
    const timeoutId = system.runTimeout(() => {
      try {
        // Check if player still exists and has flight mode active
        if (flightModeActive.has(playerId)) {
          deactivateFlightMode(player);
        }
      } catch (error) {}
      system.clearRun(timeoutId);
    }, CONFIG.DURATION);
  } catch (error) {
    console.warn(`Failed to execute event 29: ${error}`);
  }
}

/**
 * Deactivates flight mode for a player
 * @param player The player to deactivate flight mode for
 */
function deactivateFlightMode(player: Player): void {
  const playerId: string = player.id;

  flightModeActive.delete(playerId);
  slowDownValue.delete(playerId);
  flightDuration.delete(playerId);

  player.onScreenDisplay.setActionBar("§cFlying Mode deactivated!");
}

/**
 * Main flight mechanics function adapted from wings code
 * Checks if player should have flight and applies mechanics
 * @param player The player to apply flight mechanics to
 */
export function flightMechanics(player: Player): void {
  const playerId: string = player.id;

  // Only apply mechanics if flight mode is active for this player
  if (!flightModeActive.get(playerId)) {
    return;
  }

  try {
    // Check if the player is jumping or not on the ground
    const isJumping: boolean = player.isJumping;
    const isOnGround: boolean = player.isOnGround;
    const isSneaking: boolean = player.isSneaking;
    const isSprinting: boolean = player.isSprinting;

    if (isJumping || !isOnGround) {
      applyFlightMechanics(player, isJumping, isSneaking, isSprinting);
    }
  } catch (error) {
    console.warn(`Failed to apply flight mechanics for ${playerId}: ${error}`);
  }
}

/**
 * Applies the core flight mechanics adapted from wings code
 * @param player The player to apply mechanics to
 * @param isJumping Whether the player is jumping
 * @param isSneaking Whether the player is sneaking
 * @param isSprinting Whether the player is sprinting
 */
function applyFlightMechanics(
  player: Player,
  isJumping: boolean,
  isSneaking: boolean,
  isSprinting: boolean
): void {
  const playerId: string = player.id;
  const velocity: Vector3 = player.getVelocity();
  const viewDirection: Vector3 = player.getViewDirection();

  // Apply base flight mechanics
  if (isJumping) {
    player.addEffect("levitation", 5, { amplifier: 10, showParticles: false });
  }
  player.addEffect("slow_falling", 5, { amplifier: 1, showParticles: false });

  /**
   * Applies knockback to the player
   * @param x X direction component
   * @param z Z direction component
   * @param speed Speed multiplier
   * @param y Y direction component
   */
  const applyKnockback = (
    x: number,
    z: number,
    speed: number,
    y: number
  ): void => {
    player.applyKnockback(x, z, speed, y);
  };

  if (!isSneaking) {
    // Check if the player is moving significantly
    if (
      velocity &&
      (Math.abs(velocity.x) > 0.1 || Math.abs(velocity.z) > 0.1)
    ) {
      // Gradually increase the slowdown value, capping it at 1
      const currentSlowDown: number = slowDownValue.get(playerId) || 0.05;
      slowDownValue.set(playerId, Math.min(currentSlowDown + 0.1, 1));

      // Determine the speed multiplier based on whether the player is sprinting
      const speed: number = isSprinting ? 2.0 : 1.0;

      if (isJumping) {
        // Apply knockback with upward force if the player is jumping
        applyKnockback(viewDirection.x, viewDirection.z, speed, 0.6);
      } else {
        // Apply knockback with slight downward force if the player is not jumping
        applyKnockback(viewDirection.x, viewDirection.z, speed, -0.05);
      }
    }
  } else {
    // Retrieve the current slowdown value for the player
    const slowDown: number = slowDownValue.get(playerId) || 0.05;

    // Apply knockback to the player with the current slowdown value and a downward force
    player.applyKnockback(viewDirection.x, viewDirection.z, slowDown, -0.4);

    // Gradually decrease the slowdown value, ensuring it does not go below 0.05
    const currentSlowDown: number = slowDownValue.get(playerId) || 0.05;
    slowDownValue.set(playerId, Math.max(currentSlowDown - 0.03, 0.05));
  }
}

// Initialize flight mechanics system
system.runInterval(() => {
  try {
    // Get all players and apply flight mechanics if they have flight mode active
    for (const player of world.getAllPlayers()) {
      if (flightModeActive.get(player.id)) {
        flightMechanics(player);
      }
    }
  } catch (error) {
    console.warn(`Error in flight mechanics system: ${error}`);
  }
}, 1); // Run every tick
